/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 13px;
  line-height: 1.4;
  color: var(--vscode-foreground);
  background-color: var(--vscode-sideBar-background);
}

/* 应用主容器 */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* 紧凑型头部样式 */
.compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  border-bottom: 2px solid var(--vscode-sideBar-border);
  background-color: var(--vscode-sideBarSectionHeader-background);
  min-height: 36px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.app-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--vscode-sideBarSectionHeader-foreground);
}

.header-divider {
  color: var(--vscode-descriptionForeground);
  font-size: 12px;
}

.conversation-status {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

.header-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  gap: 4px;
  align-items: center;
}

.header-action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 3px;
  color: var(--vscode-icon-foreground);
  font-size: 12px;
  transition: background-color 0.2s;
}

.header-action-button:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
}

/* 废弃的头部样式已移除，使用CompactHeader组件 */

.app-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

/* 聊天窗口 */
.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.conversation-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  color: var(--vscode-descriptionForeground);
  height: 100%;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.empty-state p {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  max-width: 300px;
}

/* 对话项 */
.conversation-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 气泡式消息样式 */
.message-container {
  display: flex;
  width: 100%;
  margin-bottom: 4px;
}

.message-container.user {
  justify-content: flex-end;
}

.message-container.assistant {
  justify-content: flex-start;
}

.message-bubble {
  max-width: 80%;
  min-width: 120px;
  padding: 10px 14px;
  border-radius: 16px;
  position: relative;
  word-wrap: break-word;
}

.message-container.user .message-bubble {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border-bottom-right-radius: 4px;
}

.message-container.assistant .message-bubble {
  background-color: var(--vscode-editorWidget-background);
  color: var(--vscode-foreground);
  border: 1px solid var(--vscode-widget-border);
  border-bottom-left-radius: 4px;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 10px;
  opacity: 0.8;
}

.message-meta.user-meta {
  justify-content: flex-end;
  margin-bottom: 0;
  margin-top: 4px;
}

.operation-badge {
  background-color: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 500;
}

.timestamp {
  color: var(--vscode-descriptionForeground);
  font-size: 9px;
}

/* 用户消息时间戳特殊样式 - 在蓝色背景上更清晰 */
.message-container.user .timestamp {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.message-content {
  font-size: 13px;
  line-height: 1.4;
}

.user-text {
  color: inherit;
}

.assistant-text {
  color: inherit;
}

/* 结果卡片 */
.result-card {
  margin-top: 8px;
  border: 1px solid var(--vscode-sideBar-border);
  border-radius: 6px;
  overflow: hidden;
}

.result-card.empty {
  padding: 12px;
  text-align: center;
  color: var(--vscode-descriptionForeground);
  background-color: var(--vscode-editor-background);
}

/* 结果消息样式 */
.result-message {
  padding: 12px;
  background-color: var(--vscode-editor-background);
  border-radius: 4px;
}

.result-message p {
  margin: 0 0 8px 0;
  color: var(--vscode-foreground);
  font-size: 13px;
  line-height: 1.4;
}

.language-info {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  margin-top: 8px;
}

/* 无修改结果样式 */
.result-message.no-changes {
  text-align: center;
  padding: 20px 12px;
}

.success-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.result-message.no-changes p {
  font-size: 14px;
  font-weight: 500;
  color: var(--vscode-foreground);
  margin-bottom: 4px;
}

.result-message.no-changes .description {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  margin-top: 4px;
}

.results-header {
  padding: 8px 12px;
  background-color: var(--vscode-sideBarSectionHeader-background);
  color: var(--vscode-sideBarSectionHeader-foreground);
  font-weight: 600;
  font-size: 12px;
  border-bottom: 1px solid var(--vscode-sideBar-border);
}

.result-item {
  border-bottom: 1px solid var(--vscode-sideBar-border);
  background-color: var(--vscode-editor-background);
}

.result-item:last-child {
  border-bottom: none;
}

/* 旧的结果展示样式已移除，使用UnifiedResultSection组件样式 */

/* 输入面板 */
.input-panel {
  border-top: 2px solid var(--vscode-sideBar-border);
  background-color: var(--vscode-sideBar-background);
  padding: 10px 12px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.text-input-section {
  margin-bottom: 12px;
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.selected-indicator {
  font-size: 10px;
  color: var(--vscode-textLink-foreground);
  font-weight: normal;
}

.text-input {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: inherit;
  font-size: 12px;
  resize: vertical;
}

.text-input:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

.selected-text-display {
  position: relative;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  padding: 8px;
}

.selected-text-content {
  font-size: 12px;
  color: var(--vscode-input-foreground);
  line-height: 1.4;
  margin-bottom: 8px;
  max-height: 100px;
  overflow-y: auto;
}

.clear-selection {
  position: absolute;
  top: 4px;
  right: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  color: var(--vscode-icon-foreground);
  font-size: 10px;
}

.clear-selection:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
}

/* 旧的操作按钮样式已移除，使用新的紧凑型工具栏 */

.action-button {
  padding: 8px 12px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.action-button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.language-select {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 12px;
  min-width: 120px;
  height: 32px;
}

/* 选中文本引用样式 */
.selected-text-reference {
  margin-bottom: 12px;
  padding: 8px;
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  border-left: 3px solid var(--vscode-button-background);
}

.reference-label {
  font-size: 11px;
  font-weight: 600;
  color: var(--vscode-descriptionForeground);
  margin-bottom: 4px;
}

.reference-content {
  font-size: 12px;
  color: var(--vscode-foreground);
  font-style: italic;
  cursor: help;
}

/* 统一输入框样式 */
.unified-input-section {
  margin-bottom: 10px;
}

/* 紧凑型操作工具栏 */
.action-toolbar {
  margin-top: 8px;
}

.action-group {
  display: flex;
  gap: 6px;
  align-items: center;
  flex-wrap: wrap;
}

.compact-action-button {
  padding: 6px 10px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: background-color 0.2s;
  white-space: nowrap;
  min-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.compact-action-button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.compact-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.translate-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.inline-language-select {
  padding: 4px 6px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 3px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 10px;
  min-width: 50px;
  height: 28px;
}

.inline-language-select:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: -1px;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.unified-input {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 12px;
  font-family: var(--vscode-font-family);
  resize: vertical;
  min-height: 60px;
  line-height: 1.4;
}

.unified-input:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: -1px;
}

.submit-button {
  padding: 8px 12px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  height: fit-content;
  min-height: 32px;
  transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rewrite-input::placeholder {
  color: var(--vscode-input-placeholderForeground);
}

.rewrite-send-button {
  padding: 4px 12px;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.15s ease;
}

.rewrite-send-button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.rewrite-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.language-select {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 11px;
}

.translate-button {
  flex: 2;
}

/* 提示信息样式已移除，通过上下文提供信息 */

/* 统一结果展示组件样式 */
.unified-result-section {
  margin-top: 8px;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  overflow: hidden;
}

.section-header {
  padding: 8px 12px;
  background-color: var(--vscode-editorGroupHeader-tabsBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.items-list {
  background-color: var(--vscode-editor-background);
}

.result-item {
  border-bottom: 1px solid var(--vscode-panel-border);
}

.result-item:last-child {
  border-bottom: none;
}

.item-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.item-summary:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.item-icon {
  font-size: 12px;
  width: 16px;
  text-align: center;
}

.item-title {
  flex: 1;
  font-size: 12px;
  color: var(--vscode-foreground);
  line-height: 1.3;
}

.expand-icon {
  font-size: 10px;
  color: var(--vscode-descriptionForeground);
  width: 12px;
  text-align: center;
}

.item-details {
  padding: 8px 12px 12px 36px;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  line-height: 1.4;
  background-color: var(--vscode-editorWidget-background);
  border-top: 1px solid var(--vscode-panel-border);
}

/* 检查结果特定样式 */
.check-item.error .item-summary {
  border-left: 3px solid var(--vscode-errorForeground);
}

.check-item.warning .item-summary {
  border-left: 3px solid var(--vscode-warningForeground);
}

.check-item.info .item-summary {
  border-left: 3px solid var(--vscode-infoForeground);
}

/* 加载动画 */
.loading-spinner {
  padding: 16px;
  text-align: center;
  background-color: var(--vscode-editor-background);
  border-top: 1px solid var(--vscode-sideBar-border);
}

.spinner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--vscode-progressBar-background);
  border-top: 2px solid var(--vscode-progressBar-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-message {
  font-size: 12px;
  color: var(--vscode-foreground);
}

/* 错误消息 */
.error-message {
  padding: 8px 12px;
  background-color: var(--vscode-inputValidation-errorBackground);
  border: 1px solid var(--vscode-inputValidation-errorBorder);
  border-radius: 4px;
  margin: 8px 12px;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.error-icon {
  color: var(--vscode-errorForeground);
  flex-shrink: 0;
}

.error-text {
  flex: 1;
  color: var(--vscode-inputValidation-errorForeground);
}

.error-dismiss {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  color: var(--vscode-icon-foreground);
  font-size: 14px;
  line-height: 1;
}

.error-dismiss:hover {
  color: var(--vscode-errorForeground);
}

/* ===== Diff View 样式 ===== */
.diff-view {
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  margin: 8px 0;
  background-color: var(--vscode-editor-background);
  overflow: hidden;
}

.diff-view.no-changes {
  border-color: var(--vscode-textBlockQuote-border);
  background-color: var(--vscode-textBlockQuote-background);
}

.diff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--vscode-editorGroupHeader-tabsBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.diff-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.diff-title h3 {
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.expand-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  color: var(--vscode-icon-foreground);
  font-size: 10px;
  line-height: 1;
}

.expand-toggle:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
  border-radius: 2px;
}

.diff-stats {
  display: flex;
  gap: 8px;
  font-size: 11px;
}

.stat-insertions {
  color: var(--vscode-gitDecoration-addedResourceForeground);
  font-weight: 500;
}

.stat-deletions {
  color: var(--vscode-gitDecoration-deletedResourceForeground);
  font-weight: 500;
}

.stat-total {
  color: var(--vscode-descriptionForeground);
}

.no-changes-text {
  color: var(--vscode-descriptionForeground);
  font-size: 12px;
  font-style: italic;
}

.diff-content {
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.diff-text {
  font-family: var(--vscode-editor-font-family);
  font-size: var(--vscode-editor-font-size);
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.diff-segment {
  position: relative;
}

.diff-insert {
  background-color: var(--vscode-diffEditor-insertedTextBackground);
  color: var(
    --vscode-diffEditor-insertedTextForeground,
    var(--vscode-foreground)
  );
  border-radius: 2px;
  padding: 1px 2px;
}

.diff-delete {
  background-color: var(--vscode-diffEditor-removedTextBackground);
  color: var(
    --vscode-diffEditor-removedTextForeground,
    var(--vscode-foreground)
  );
  text-decoration: line-through;
  border-radius: 2px;
  padding: 1px 2px;
}

.diff-equal {
  /* 保持默认样式 */
}

.diff-actions {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--vscode-editorWidget-background);
  border-top: 1px solid var(--vscode-panel-border);
}

.btn {
  padding: 6px 12px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.15s ease;
}

.btn-accept {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn-accept:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.btn-reject {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.btn-reject:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.processing {
  opacity: 0.8;
  cursor: wait;
}

/* ===== Issues Section 样式 ===== */
.issues-section {
  margin-top: 12px;
  padding: 12px;
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
}

.issues-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.issues-section ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.issue-item {
  padding: 8px 0;
  border-bottom: 1px solid var(--vscode-panel-border);
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.issue-item:last-child {
  border-bottom: none;
}

.issue-message {
  font-size: 12px;
  color: var(--vscode-foreground);
  font-weight: 500;
}

.issue-suggestion {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

/* ChatView组件样式已移除，该组件已废弃 */

/* ChatView特有的消息样式已移除，使用新的气泡式Message组件样式 */

/* 认证状态样式 - 紧凑型 */
.auth-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: none;
  font-size: 11px;
  border: none;
}

.auth-status.loading {
  justify-content: center;
  gap: 4px;
}

.auth-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--vscode-progressBar-background);
  border-top: 2px solid var(--vscode-progressBar-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.auth-status.authenticated {
  color: var(--vscode-statusBar-foreground);
}

.auth-status.not-authenticated {
  color: var(--vscode-errorForeground);
  flex-direction: row;
  gap: 4px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  flex: 1;
}

.user-info:hover {
  opacity: 0.8;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 500;
  color: var(--vscode-foreground);
}

.user-email {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.auth-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auth-button.login {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.auth-button.login:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.auth-button.logout {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.auth-button.logout:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.auth-message {
  color: var(--vscode-descriptionForeground);
  text-align: center;
  margin-bottom: 4px;
}

/* 认证提示样式 */
.auth-required-notice {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  margin-bottom: 16px;
}

.notice-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-weight: 500;
  color: var(--vscode-foreground);
  margin-bottom: 2px;
}

.notice-description {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

/* 修改说明样式 - 紧凑版 */
.changes-section {
  margin-top: 8px;
  padding: 10px;
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
}

.changes-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: var(--vscode-foreground);
  display: flex;
  align-items: center;
  gap: 6px;
}

.changes-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.change-item {
  padding: 8px 10px;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  transition: border-color 0.2s ease;
}

.change-item:hover {
  border-color: var(--vscode-focusBorder);
}

.change-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 4px;
}

.change-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 4px;
}

.change-type-structure {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007acc;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

.change-type-clarity {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ff8c00;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.change-type-conciseness {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.change-type-grammar {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.change-type-tone {
  background-color: rgba(102, 16, 242, 0.1);
  color: #6610f2;
  border: 1px solid rgba(102, 16, 242, 0.2);
}

.change-type-style {
  background-color: rgba(214, 51, 132, 0.1);
  color: #d63384;
  border: 1px solid rgba(214, 51, 132, 0.2);
}

.change-description {
  font-size: 12px;
  color: var(--vscode-foreground);
  font-weight: 500;
  line-height: 1.3;
  flex: 1;
}

.change-reason {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  line-height: 1.3;
  font-style: italic;
  padding-left: 12px;
  margin-top: 2px;
  border-left: 2px solid var(--vscode-panel-border);
}
