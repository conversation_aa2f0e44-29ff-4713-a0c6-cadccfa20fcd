import * as vscode from 'vscode';
import { AuthService } from './AuthService';

/**
 * OAuth认证服务
 * 使用VSCode webview完成openEuler SSO认证流程
 */
export class OAuthService implements vscode.UriHandler {
  private static instance: OAuthService;
  private authService: AuthService;
  private pendingAuth: Promise<void> | null = null;
  private authResolve: ((value: void | PromiseLike<void>) => void) | null = null;
  private authReject: ((reason?: any) => void) | null = null;
  private authWebview: vscode.WebviewPanel | null = null;

  // openEuler OAuth配置
  private static readonly CLIENT_ID = '655d637668e3addd8ee560cb';
  private static readonly LOGIN_BASE_URL = 'https://id.openeuler.org/login';

  private constructor(authService: AuthService) {
    this.authService = authService;
  }

  public static getInstance(authService: AuthService): OAuthService {
    if (!OAuthService.instance) {
      OAuthService.instance = new OAuthService(authService);
    }
    return OAuthService.instance;
  }

  /**
   * 处理URI回调
   */
  public handleUri(uri: vscode.Uri): vscode.ProviderResult<void> {
    console.log('OAuthService: Received URI callback:', uri.toString());

    try {
      // 解析URI参数
      const query = new URLSearchParams(uri.query);
      const fragment = new URLSearchParams(uri.fragment);
      
      // 尝试从query或fragment中提取Cookie信息
      const sessionCookie = query.get('session_cookie') || 
                           fragment.get('session_cookie') || 
                           query.get('_Y_G_') || 
                           fragment.get('_Y_G_');
                           
      const token = query.get('token') || 
                   fragment.get('token') || 
                   query.get('_U_T_') || 
                   fragment.get('_U_T_');

      if (sessionCookie) {
        this.handleAuthSuccess(sessionCookie, token || undefined);
      } else {
        // 如果没有直接的参数，尝试从完整URI中提取
        this.handleAuthCallback(uri);
      }
    } catch (error) {
      console.error('OAuthService: Error handling URI:', error);
      this.handleAuthError(error);
    }
  }

  /**
   * 启动OAuth登录流程
   */
  public async startLogin(): Promise<void> {
    try {
      // 如果已有进行中的认证，等待完成
      if (this.pendingAuth) {
        return this.pendingAuth;
      }

      // 创建新的认证Promise
      this.pendingAuth = new Promise<void>((resolve, reject) => {
        this.authResolve = resolve;
        this.authReject = reject;
      });

      // 构建完整的登录URL，包含client_id和redirect_uri
      const callbackUri = this.getCallbackUri();

      // 构建登录URL，使用openEuler的OAuth参数格式
      const fullLoginUrl = `${OAuthService.LOGIN_BASE_URL}?client_id=${OAuthService.CLIENT_ID}&redirect_uri=${encodeURIComponent(callbackUri)}&lang=zh`;

      console.log('OAuthService: Opening login URL in webview:', fullLoginUrl);

      // 使用webview打开登录页面
      await this.openLoginWebview(fullLoginUrl);

      // 等待webview登录完成
      return this.pendingAuth;

    } catch (error) {
      console.error('OAuthService: Login failed:', error);

      // 询问用户是否要手动输入
      const errorMessage = error instanceof Error ? error.message : '登录失败';
      const fallback = await vscode.window.showErrorMessage(
        `自动登录失败: ${errorMessage}\n\n是否尝试手动输入Cookie？`,
        '手动输入',
        '取消'
      );

      if (fallback === '手动输入') {
        await this.promptForManualInput();
        return;
      }

      this.handleAuthError(error);
      throw error;
    }
  }

  /**
   * 获取回调URI
   */
  private getCallbackUri(): string {
    // 使用VSCode的URI scheme
    return `vscode://openeuler.docmate/auth-callback`;
  }

  /**
   * 使用webview打开登录页面
   */
  private async openLoginWebview(loginUrl: string): Promise<void> {
    // 创建webview面板
    this.authWebview = vscode.window.createWebviewPanel(
      'openeulerLogin',
      'openEuler 登录',
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        enableCommandUris: true,
        enableFindWidget: true
      }
    );

    // 设置webview内容 - 直接导航到登录页面
    this.authWebview.webview.html = this.getWebviewContent(loginUrl);

    // 监听webview关闭事件
    this.authWebview.onDidDispose(() => {
      this.authWebview = null;
      if (this.pendingAuth) {
        this.handleAuthError(new Error('用户关闭了登录窗口'));
      }
    });

    // 监听webview消息
    this.authWebview.webview.onDidReceiveMessage(async (message) => {
      console.log('OAuthService: Received webview message:', message);

      if (message.type === 'openExternal') {
        // 在外部浏览器中打开登录页面
        await vscode.env.openExternal(vscode.Uri.parse(message.url));
      } else if (message.type === 'navigation') {
        await this.handleWebviewNavigation(message.url);
      } else if (message.type === 'error') {
        this.handleAuthError(new Error(message.error));
      } else if (message.type === 'ready') {
        console.log('OAuthService: Webview is ready');
      } else if (message.type === 'loginSuccess') {
        // 用户手动确认登录成功
        await this.handleManualLoginSuccess();
      } else if (message.type === 'manualInput') {
        // 用户选择手动输入
        if (this.authWebview) {
          this.authWebview.dispose();
          this.authWebview = null;
        }
        await this.promptForManualInput();
      }
    });

    console.log('OAuthService: Webview created and configured');
  }

  /**
   * 生成webview的HTML内容
   */
  private getWebviewContent(loginUrl: string): string {
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>openEuler 登录</title>
        <style>
          body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          }
          .container {
            padding: 20px;
            text-align: center;
          }
          .title {
            font-size: 18px;
            font-weight: 600;
            color: #24292e;
            margin-bottom: 20px;
          }
          .instructions {
            background: #f6f8fa;
            border: 1px solid #d1d5da;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
            text-align: left;
            font-size: 14px;
            line-height: 1.5;
          }
          .btn {
            padding: 8px 16px;
            font-size: 14px;
            border: 1px solid #d1d5da;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            color: #24292e;
            margin: 0 8px;
            text-decoration: none;
            display: inline-block;
          }
          .btn:hover {
            background: #f6f8fa;
          }
          .btn.primary {
            background: #0366d6;
            color: white;
            border-color: #0366d6;
          }
          .btn.primary:hover {
            background: #0256cc;
          }
          .status {
            margin-top: 20px;
            padding: 12px;
            border-radius: 6px;
            font-size: 14px;
          }
          .status.info {
            background: #e1f5fe;
            border: 1px solid #81d4fa;
            color: #01579b;
          }
          .status.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
          }
          .status.error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="title">openEuler 登录</div>

          <div class="instructions">
            <h3>登录步骤：</h3>
            <ol>
              <li>点击下方"打开登录页面"按钮</li>
              <li>在新打开的浏览器窗口中完成openEuler登录</li>
              <li>登录成功后，返回此页面点击"我已完成登录"</li>
              <li>或者选择"手动输入Cookie"进行手动认证</li>
            </ol>
          </div>

          <div>
            <a href="${loginUrl}" class="btn primary" id="openLogin" target="_blank">打开登录页面</a>
            <button class="btn primary" id="confirmLogin" style="display: none;">我已完成登录</button>
            <button class="btn" id="manualInput">手动输入Cookie</button>
          </div>

          <div class="status info" id="status">
            点击"打开登录页面"开始登录流程
          </div>
        </div>

        <script>
          const vscode = acquireVsCodeApi();
          const openBtn = document.getElementById('openLogin');
          const confirmBtn = document.getElementById('confirmLogin');
          const manualBtn = document.getElementById('manualInput');
          const status = document.getElementById('status');

          // 打开登录页面
          openBtn.onclick = function(e) {
            e.preventDefault();
            // 在外部浏览器中打开登录页面
            vscode.postMessage({ type: 'openExternal', url: '${loginUrl}' });

            // 显示确认按钮
            openBtn.style.display = 'none';
            confirmBtn.style.display = 'inline-block';

            status.textContent = '请在浏览器中完成登录，然后返回点击"我已完成登录"';
            status.className = 'status info';
          };

          // 确认登录成功按钮
          confirmBtn.onclick = function() {
            status.textContent = '正在验证登录状态...';
            status.className = 'status info';
            confirmBtn.disabled = true;
            vscode.postMessage({ type: 'loginSuccess' });
          };

          // 手动输入按钮
          manualBtn.onclick = function() {
            vscode.postMessage({ type: 'manualInput' });
          };

          // 监听来自扩展的消息
          window.addEventListener('message', function(event) {
            const message = event.data;
            if (message.type === 'callbackReceived') {
              status.textContent = message.message;
              status.className = 'status success';

              // 高亮确认按钮
              confirmBtn.style.background = '#28a745';
              confirmBtn.style.borderColor = '#28a745';
              confirmBtn.style.animation = 'pulse 1s infinite';
            }
          });

          // 添加脉冲动画
          const style = document.createElement('style');
          style.textContent = \`
            @keyframes pulse {
              0% { transform: scale(1); }
              50% { transform: scale(1.05); }
              100% { transform: scale(1); }
            }
          \`;
          document.head.appendChild(style);

          // 通知webview已准备就绪
          vscode.postMessage({ type: 'ready' });
        </script>
      </body>
      </html>
    `;
  }

  /**
   * 处理webview传来的cookie信息
   */
  private async handleWebviewCookies(cookies: Record<string, string>): Promise<void> {
    try {
      console.log('OAuthService: Processing cookies from webview:', Object.keys(cookies));

      // 提取openEuler的认证cookie
      const sessionCookie = cookies['_Y_G_'];
      const token = cookies['_U_T_'];

      if (sessionCookie) {
        console.log('OAuthService: Found session cookie, proceeding with authentication');

        // 关闭webview
        if (this.authWebview) {
          this.authWebview.dispose();
          this.authWebview = null;
        }

        // 处理认证成功
        await this.handleAuthSuccess(sessionCookie, token);
      } else {
        console.log('OAuthService: No valid session cookie found in:', Object.keys(cookies));
        this.handleAuthError(new Error('未找到有效的认证信息'));
      }
    } catch (error) {
      console.error('OAuthService: Error processing webview cookies:', error);
      this.handleAuthError(error);
    }
  }

  /**
   * 处理webview导航变化
   */
  private async handleWebviewNavigation(url: string): Promise<void> {
    console.log('OAuthService: Webview navigated to:', url);

    // 检查是否导航到了回调URL
    if (url.includes('auth-callback') || url.includes('callback')) {
      console.log('OAuthService: Detected callback navigation');
      // 这里可以添加更多的回调检测逻辑
    }
  }

  /**
   * 处理用户手动确认登录成功
   */
  private async handleManualLoginSuccess(): Promise<void> {
    try {
      console.log('OAuthService: User confirmed login success, attempting to verify authentication');

      // 关闭webview
      if (this.authWebview) {
        this.authWebview.dispose();
        this.authWebview = null;
      }

      // 尝试通过后端验证用户是否已登录
      // 这里我们可以让用户提供一个临时验证码或者直接进入手动输入
      const choice = await vscode.window.showInformationMessage(
        '请选择认证方式：',
        { modal: true },
        '自动检测登录状态',
        '手动输入Cookie'
      );

      if (choice === '自动检测登录状态') {
        await this.attemptAutoDetectLogin();
      } else if (choice === '手动输入Cookie') {
        await this.promptForManualInput();
      } else {
        this.handleAuthError(new Error('用户取消登录'));
      }

    } catch (error) {
      console.error('OAuthService: Error handling manual login success:', error);
      this.handleAuthError(error);
    }
  }

  /**
   * 尝试自动检测登录状态
   */
  private async attemptAutoDetectLogin(): Promise<void> {
    try {
      // 显示一个输入框让用户输入验证信息
      const verificationInfo = await vscode.window.showInputBox({
        prompt: '请输入您在openEuler网站上看到的用户名或邮箱（用于验证登录状态）',
        placeHolder: '例如: your-username 或 <EMAIL>',
        ignoreFocusOut: true,
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入用户名或邮箱';
          }
          return null;
        }
      });

      if (!verificationInfo) {
        throw new Error('未提供验证信息');
      }

      // 这里可以调用后端API尝试验证
      // 暂时还是回退到手动输入
      vscode.window.showWarningMessage(
        '自动检测功能正在开发中，请使用手动输入方式。',
        '确定'
      );

      await this.promptForManualInput();

    } catch (error) {
      console.error('OAuthService: Auto detect login failed:', error);
      await this.promptForManualInput();
    }
  }

  /**
   * 处理认证成功
   */
  private async handleAuthSuccess(sessionCookie: string, token?: string): Promise<void> {
    try {
      console.log('OAuthService: Authentication successful');
      
      // 调用AuthService进行登录
      await this.authService.loginWithSSOCredentials(sessionCookie, token);
      
      // 显示成功消息
      vscode.window.showInformationMessage('登录成功！');
      
      // 解决认证Promise
      if (this.authResolve) {
        this.authResolve();
      }
    } catch (error) {
      console.error('OAuthService: Login with credentials failed:', error);
      this.handleAuthError(error);
    } finally {
      this.clearPendingAuth();
    }
  }

  /**
   * 处理认证错误
   */
  private handleAuthError(error: any): void {
    console.error('OAuthService: Authentication error:', error);
    
    const errorMessage = error instanceof Error ? error.message : '登录失败';
    vscode.window.showErrorMessage(`登录失败: ${errorMessage}`);
    
    if (this.authReject) {
      this.authReject(error);
    }
    
    this.clearPendingAuth();
  }

  /**
   * 处理认证回调（当没有直接参数时）
   */
  private handleAuthCallback(uri: vscode.Uri): void {
    console.log('OAuthService: Processing auth callback:', uri.toString());

    // 检查是否有正在进行的webview认证
    if (this.authWebview && this.pendingAuth) {
      console.log('OAuthService: Callback received during webview auth, notifying webview');

      // 通知webview用户已完成外部登录
      this.authWebview.webview.postMessage({
        type: 'callbackReceived',
        message: '检测到登录回调，请点击"我已完成登录"按钮继续'
      });

      return;
    }

    // 如果没有正在进行的认证，显示手动输入消息
    console.log('OAuthService: No pending auth, showing manual input');
    this.promptForManualInput();
  }

  /**
   * 提示用户手动输入Cookie
   */
  private async promptForManualInput(): Promise<void> {
    try {
      // 首先显示详细的操作指导
      const proceed = await vscode.window.showInformationMessage(
        '手动获取Cookie步骤：\n\n' +
        `1. 在浏览器中访问 ${OAuthService.LOGIN_BASE_URL}\n` +
        '2. 完成登录\n' +
        '3. 按F12打开开发者工具\n' +
        '4. 切换到"应用程序"(Application)标签\n' +
        '5. 在左侧展开"Cookie"\n' +
        '6. 找到_Y_G_和_U_T_的值\n\n' +
        '准备好后点击"输入Cookie"',
        { modal: true },
        '输入Cookie',
        '打开登录页面',
        '取消'
      );

      if (proceed === '打开登录页面') {
        // 打开登录页面
        await vscode.env.openExternal(vscode.Uri.parse(OAuthService.LOGIN_BASE_URL));

        // 再次询问是否准备输入
        const ready = await vscode.window.showInformationMessage(
          '登录页面已打开，完成登录后请按照上述步骤获取Cookie',
          { modal: true },
          '输入Cookie',
          '取消'
        );

        if (ready !== '输入Cookie') {
          throw new Error('用户取消手动输入');
        }
      } else if (proceed !== '输入Cookie') {
        throw new Error('用户取消手动输入');
      }

      // 输入_Y_G_ Cookie
      const sessionCookie = await vscode.window.showInputBox({
        prompt: '请输入_Y_G_ Cookie的值',
        placeHolder: '例如: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        ignoreFocusOut: true,
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入_Y_G_ Cookie值';
          }
          if (value.length < 10) {
            return 'Cookie值似乎太短，请检查是否完整';
          }
          return null;
        }
      });

      if (!sessionCookie) {
        throw new Error('未提供会话Cookie');
      }

      // 输入_U_T_ Token（可选）
      const token = await vscode.window.showInputBox({
        prompt: '请输入_U_T_ Token的值（可选，如果没有可以留空）',
        placeHolder: '例如: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        ignoreFocusOut: true
      });

      // 显示登录进度
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: '正在验证登录信息...',
        cancellable: false
      }, async (progress) => {
        progress.report({ increment: 0, message: '验证Cookie...' });

        await this.handleAuthSuccess(sessionCookie.trim(), token?.trim() || undefined);

        progress.report({ increment: 100, message: '登录成功！' });
      });

    } catch (error) {
      console.error('OAuthService: Manual input failed:', error);

      if (error instanceof Error && error.message.includes('取消')) {
        // 用户主动取消，不显示错误
        this.clearPendingAuth();
      } else {
        // 其他错误，询问是否重试
        const retry = await vscode.window.showErrorMessage(
          `手动登录失败: ${error instanceof Error ? error.message : '未知错误'}`,
          '重试',
          '取消'
        );

        if (retry === '重试') {
          await this.promptForManualInput();
        } else {
          this.handleAuthError(error);
        }
      }
    }
  }

  /**
   * 清除待处理的认证
   */
  private clearPendingAuth(): void {
    this.pendingAuth = null;
    this.authResolve = null;
    this.authReject = null;

    // 清理webview
    if (this.authWebview) {
      this.authWebview.dispose();
      this.authWebview = null;
    }
  }

  /**
   * 取消当前认证
   */
  public cancelAuth(): void {
    if (this.pendingAuth) {
      this.handleAuthError(new Error('用户取消登录'));
    }
  }
}
